import request from "@/utils/httpRequest";

export default {
	save: function (inputForm) {
		return request({
			url: "/route/tbRouteEnvironment/save",
			method: "post",
			data: inputForm,
		});
	},

	delete: function (ids) {
		return request({
			url: "/route/tbRouteEnvironment/delete",
			method: "delete",
			params: { ids: ids },
		});
	},

	queryById: function (id) {
		return request({
			url: "/route/tbRouteEnvironment/queryById",
			method: "get",
			params: { id: id },
		});
	},

	list: function (params) {
		return request({
			url: "/route/tbRouteEnvironment/list",
			method: "get",
			params: params,
		});
	},

	exportTemplate: function () {
		return request({
			url: "/route/tbRouteEnvironment/import/template",
			method: "get",
			responseType: "blob",
		});
	},

	exportExcel: function (params) {
		return request({
			url: "/route/tbRouteEnvironment/export",
			method: "get",
			params: params,
			responseType: "blob",
		});
	},

	importExcel: function (data) {
		return request({
			url: "/route/tbRouteEnvironment/import",
			method: "post",
			data: data,
		});
	},
};
