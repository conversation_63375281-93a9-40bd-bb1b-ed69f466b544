import request from "@/utils/httpRequest";

export default {
	save: function (inputForm) {
		return request({
			url: "/device/tbDeviceInfo/save",
			method: "post",
			data: inputForm,
		});
	},

	delete: function (ids) {
		return request({
			url: "/device/tbDeviceInfo/delete",
			method: "delete",
			params: { ids: ids },
		});
	},

	queryById: function (id) {
		return request({
			url: "/device/tbDeviceInfo/queryById",
			method: "get",
			params: { id: id },
		});
	},

	list: function (params) {
		return request({
			url: "/device/tbDeviceInfo/list",
			method: "get",
			params: params,
		});
	},

	exportTemplate: function () {
		return request({
			url: "/device/tbDeviceInfo/import/template",
			method: "get",
			responseType: "blob",
		});
	},

	exportExcel: function (params) {
		return request({
			url: "/device/tbDeviceInfo/export",
			method: "get",
			params: params,
			responseType: "blob",
		});
	},

	importExcel: function (data) {
		return request({
			url: "/device/tbDeviceInfo/import",
			method: "post",
			data: data,
		});
	},
};
