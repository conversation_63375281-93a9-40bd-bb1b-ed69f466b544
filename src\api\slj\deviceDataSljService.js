import request from "@/utils/httpRequest";

export default {
	save: function (inputForm) {
		return request({
			url: "/slj/deviceDataSlj/save",
			method: "post",
			data: inputForm,
		});
	},

	delete: function (ids) {
		return request({
			url: "/slj/deviceDataSlj/delete",
			method: "delete",
			params: { ids: ids },
		});
	},

	queryById: function (id) {
		return request({
			url: "/slj/deviceDataSlj/queryById",
			method: "get",
			params: { id: id },
		});
	},

	list: function (params) {
		return request({
			url: "/slj/deviceDataSlj/list",
			method: "get",
			params: params,
		});
	},

	exportTemplate: function () {
		return request({
			url: "/slj/deviceDataSlj/import/template",
			method: "get",
			responseType: "blob",
		});
	},

	exportExcel: function (params) {
		return request({
			url: "/slj/deviceDataSlj/export",
			method: "get",
			params: params,
			responseType: "blob",
		});
	},

	importExcel: function (data) {
		return request({
			url: "/slj/deviceDataSlj/import",
			method: "post",
			data: data,
		});
	},
};
