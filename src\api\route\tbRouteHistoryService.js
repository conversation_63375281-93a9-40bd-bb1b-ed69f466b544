import request from "@/utils/httpRequest";

export default {
	save: function (inputForm) {
		return request({
			url: "/route/tbRouteHistory/save",
			method: "post",
			data: inputForm,
		});
	},

	delete: function (ids) {
		return request({
			url: "/route/tbRouteHistory/delete",
			method: "delete",
			params: { ids: ids },
		});
	},

	queryById: function (id) {
		return request({
			url: "/route/tbRouteHistory/queryById",
			method: "get",
			params: { id: id },
		});
	},

	list: function (params) {
		return request({
			url: "/route/tbRouteHistory/list",
			method: "get",
			params: params,
		});
	},

	exportTemplate: function () {
		return request({
			url: "/route/tbRouteHistory/import/template",
			method: "get",
			responseType: "blob",
		});
	},

	exportExcel: function (params) {
		return request({
			url: "/route/tbRouteHistory/export",
			method: "get",
			params: params,
			responseType: "blob",
		});
	},

	importExcel: function (data) {
		return request({
			url: "/route/tbRouteHistory/import",
			method: "post",
			data: data,
		});
	},
};
