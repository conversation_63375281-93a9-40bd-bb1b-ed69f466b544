import request from "@/utils/httpRequest";

export default {
	save: function (inputForm) {
		return request({
			url: "/categorycfg/tbCategoryCfg/save",
			method: "post",
			data: inputForm,
		});
	},

	delete: function (ids) {
		return request({
			url: "/categorycfg/tbCategoryCfg/delete",
			method: "delete",
			params: { ids: ids },
		});
	},

	queryById: function (id) {
		return request({
			url: "/categorycfg/tbCategoryCfg/queryById",
			method: "get",
			params: { id: id },
		});
	},

	list: function (params) {
		return request({
			url: "/categorycfg/tbCategoryCfg/list",
			method: "get",
			params: params,
		});
	},

	exportTemplate: function () {
		return request({
			url: "/categorycfg/tbCategoryCfg/import/template",
			method: "get",
			responseType: "blob",
		});
	},

	exportExcel: function (params) {
		return request({
			url: "/categorycfg/tbCategoryCfg/export",
			method: "get",
			params: params,
			responseType: "blob",
		});
	},

	importExcel: function (data) {
		return request({
			url: "/categorycfg/tbCategoryCfg/import",
			method: "post",
			data: data,
		});
	},
};
