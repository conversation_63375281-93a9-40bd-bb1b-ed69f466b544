import request from "@/utils/httpRequest";

export default {
	save: function (inputForm) {
		return request({
			url: "/category/tbDeviceCategory/save",
			method: "post",
			data: inputForm,
		});
	},

	delete: function (ids) {
		return request({
			url: "/category/tbDeviceCategory/delete",
			method: "delete",
			params: { ids: ids },
		});
	},

	queryById: function (id) {
		return request({
			url: "/category/tbDeviceCategory/queryById",
			method: "get",
			params: { id: id },
		});
	},

	list: function (params) {
		return request({
			url: "/category/tbDeviceCategory/list",
			method: "get",
			params: params,
		});
	},

	exportTemplate: function () {
		return request({
			url: "/category/tbDeviceCategory/import/template",
			method: "get",
			responseType: "blob",
		});
	},

	exportExcel: function (params) {
		return request({
			url: "/category/tbDeviceCategory/export",
			method: "get",
			params: params,
			responseType: "blob",
		});
	},

	importExcel: function (data) {
		return request({
			url: "/category/tbDeviceCategory/import",
			method: "post",
			data: data,
		});
	},
};
