import request from "@/utils/httpRequest";

export default {
	save: function (inputForm) {
		return request({
			url: "/route/tbRouteKeyArea/save",
			method: "post",
			data: inputForm,
		});
	},

	delete: function (ids) {
		return request({
			url: "/route/tbRouteKeyArea/delete",
			method: "delete",
			params: { ids: ids },
		});
	},

	queryById: function (id) {
		return request({
			url: "/route/tbRouteKeyArea/queryById",
			method: "get",
			params: { id: id },
		});
	},

	list: function (params) {
		return request({
			url: "/route/tbRouteKeyArea/list",
			method: "get",
			params: params,
		});
	},

	exportTemplate: function () {
		return request({
			url: "/route/tbRouteKeyArea/import/template",
			method: "get",
			responseType: "blob",
		});
	},

	exportExcel: function (params) {
		return request({
			url: "/route/tbRouteKeyArea/export",
			method: "get",
			params: params,
			responseType: "blob",
		});
	},

	importExcel: function (data) {
		return request({
			url: "/route/tbRouteKeyArea/import",
			method: "post",
			data: data,
		});
	},
};
