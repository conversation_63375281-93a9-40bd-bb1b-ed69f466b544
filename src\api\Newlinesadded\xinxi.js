import request from "@/utils/httpRequest";

export default {
	save: function (inputForm) {
		return request({
			url: "/route/tbRoute/save",
			method: "post",
			data: inputForm,
		});
	},

	delete: function (ids) {
		return request({
			url: "/route/tbRoute/delete",
			method: "delete",
			params: { ids: ids },
		});
	},

	queryById: function (id) {
		return request({
			url: "/route/tbRoute/queryById",
			method: "get",
			params: { id: id },
		});
	},

	list: function (params) {
		return request({
			url: "/route/tbRoute/list",
			method: "get",
			params: params,
		});
	},

	exportTemplate: function () {
		return request({
			url: "/route/tbRoute/import/template",
			method: "get",
			responseType: "blob",
		});
	},

	exportExcel: function (params) {
		return request({
			url: "/route/tbRoute/export",
			method: "get",
			params: params,
			responseType: "blob",
		});
	},

	importExcel: function (data) {
		return request({
			url: "/route/tbRoute/import",
			method: "post",
			data: data,
		});
	},
};
